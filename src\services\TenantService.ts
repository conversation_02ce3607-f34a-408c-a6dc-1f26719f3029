import { supabaseAdmin } from '@/config/supabase';
import { logger } from '@/utils/logger';

export interface Tenant {
  id: string;
  name: string;
  slug: string;
  is_active: boolean;
  subscription_plan: string;
  subscription_status: string;
  created_at: string;
  updated_at: string;
}

export class TenantService {
  /**
   * L<PERSON><PERSON> thông tin tenant theo ID
   */
  async getTenantById(tenantId: string): Promise<Tenant | null> {
    try {
      logger.info('Getting tenant by ID', { tenantId });

      const { data, error } = await supabaseAdmin
        .from('tenants')
        .select('*')
        .eq('id', tenantId)
        .eq('is_active', true)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          logger.warn('Tenant not found', { tenantId });
          return null;
        }
        throw error;
      }

      logger.info('Tenant retrieved successfully', {
        tenantId: data.id,
        name: data.name,
        slug: data.slug
      });

      return data;
    } catch (error: any) {
      logger.error('Failed to get tenant by ID', {
        tenantId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Lấy tenant theo slug
   */
  async getTenantBySlug(slug: string): Promise<Tenant | null> {
    try {
      logger.info('Getting tenant by slug', { slug });

      const { data, error } = await supabaseAdmin
        .from('tenants')
        .select('*')
        .eq('slug', slug)
        .eq('is_active', true)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          logger.warn('Tenant not found by slug', { slug });
          return null;
        }
        throw error;
      }

      logger.info('Tenant retrieved successfully by slug', {
        tenantId: data.id,
        name: data.name,
        slug: data.slug
      });

      return data;
    } catch (error: any) {
      logger.error('Failed to get tenant by slug', {
        slug,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Validate tenant ID
   */
  async validateTenantId(tenantId: string): Promise<boolean> {
    try {
      const tenant = await this.getTenantById(tenantId);
      return tenant !== null;
    } catch (error) {
      logger.error('Failed to validate tenant ID', {
        tenantId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return false;
    }
  }

  /**
   * Lấy danh sách tenants
   */
  async getAllTenants(): Promise<Tenant[]> {
    try {
      logger.info('Getting all tenants');

      const { data, error } = await supabaseAdmin
        .from('tenants')
        .select('*')
        .eq('is_active', true)
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      }

      logger.info('Retrieved all tenants', {
        count: data?.length || 0
      });

      return data || [];
    } catch (error: any) {
      logger.error('Failed to get all tenants', {
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Lấy tenant mặc định (tenant đầu tiên được tạo)
   */
  async getDefaultTenant(): Promise<Tenant | null> {
    try {
      logger.info('Getting default tenant');

      const { data, error } = await supabaseAdmin
        .from('tenants')
        .select('*')
        .eq('is_active', true)
        .order('created_at', { ascending: true })
        .limit(1)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          logger.warn('No default tenant found');
          return null;
        }
        throw error;
      }

      logger.info('Default tenant retrieved', {
        tenantId: data.id,
        name: data.name,
        slug: data.slug
      });

      return data;
    } catch (error: any) {
      logger.error('Failed to get default tenant', {
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Tạo tenant context cho service operations
   */
  async setTenantContext(tenantId: string): Promise<void> {
    try {
      // Validate tenant exists
      const isValid = await this.validateTenantId(tenantId);
      if (!isValid) {
        throw new Error(`Invalid tenant ID: ${tenantId}`);
      }

      // Set context for service role
      await supabaseAdmin.rpc('set_config', {
        setting_name: 'app.current_tenant_id',
        setting_value: tenantId,
        is_local: true
      });

      logger.info('Tenant context set successfully', { tenantId });
    } catch (error: any) {
      logger.error('Failed to set tenant context', {
        tenantId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Lấy tenant context hiện tại
   */
  async getCurrentTenantId(): Promise<string | null> {
    try {
      const { data, error } = await supabaseAdmin.rpc('current_setting', {
        setting_name: 'app.current_tenant_id'
      });

      if (error) {
        logger.warn('Failed to get current tenant context', {
          error: error.message
        });
        return null;
      }

      return data || null;
    } catch (error: any) {
      logger.warn('Failed to get current tenant context', {
        error: error.message
      });
      return null;
    }
  }
}

// Export singleton instance
export const tenantService = new TenantService();
