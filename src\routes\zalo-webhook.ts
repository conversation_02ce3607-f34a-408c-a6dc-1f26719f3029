import { Router, Request, Response } from 'express';
import { as<PERSON>Hand<PERSON> } from '../middleware/errorHandler';
import { AppError } from '../middleware/errorHandler';
import { validateTenant, getTenantIdFromRequest } from '../middleware/tenantMiddleware';
import { logger } from '../utils/logger';
import axios from 'axios';
import { ChatwootService } from '../services/ChatwootService';
import { chatwootConfig, validateChatwootConfig } from '../config/chatwoot';
import '../types/api';

// Declare Node.js globals
declare const process: any;

const router = Router();

// Khởi tạo Chatwoot service
let chatwootService: ChatwootService | null = null;

// Kiểm tra và khởi tạo Chatwoot service nếu được bật
if (chatwootConfig.enabled && validateChatwootConfig()) {
  chatwootService = new ChatwootService(chatwootConfig);
  logger.info('Chatwoot integration enabled', {
    baseUrl: chatwootConfig.baseUrl,
    inboxId: chatwootConfig.inboxId
  });
} else {
  logger.info('Chatwoot integration disabled or misconfigured');
}

// Interface cho tin nhắn Zalo
interface ZaloMessage {
  type: number;
  data: {
    actionId: string;
    msgId: string;
    cliMsgId: string;
    msgType: string;
    uidFrom: string;
    idTo: string;
    dName: string;
    ts: string;
    status: number;
    content: string;
    notify: string;
    ttl: number;
    userId: string;
    uin: string;
    topOut: string;
    topOutTimeOut: string;
    topOutImprTimeOut: string;
    propertyExt: {
      color: number;
      size: number;
      type: number;
      subType: number;
      ext: string;
    };
    paramsExt: {
      countUnread: number;
      containType: number;
      platformType: number;
    };
    cmd: number;
    st: number;
    at: number;
    realMsgId: string;
  };
  threadId: string;
  isSelf: boolean;
}

// Interface cho phản hồi webhook
interface WebhookResponse {
  message: string;
  timestamp: string;
  originalMessage?: any;
}

// POST /api/zalo-webhook - Nhận tin nhắn từ Zalo
router.post('/', validateTenant, asyncHandler(async (req: Request, res: Response) => {
  const messageData: ZaloMessage = req.body;
  
  logger.info('Received Zalo message', { 
    requestId: req.id, 
    messageData: JSON.stringify(messageData, null, 2)
  });

  // Validate dữ liệu đầu vào
  if (!messageData || !messageData.data) {
    throw new AppError('Invalid message format', 400, 'INVALID_MESSAGE_FORMAT');
  }

  const { data, threadId, isSelf } = messageData;
  const { content, dName, uidFrom, msgType, ts } = data;

  // Log thông tin chi tiết tin nhắn
  logger.info('Message details', {
    requestId: req.id,
    from: dName,
    userId: uidFrom,
    content: content,
    messageType: msgType,
    timestamp: ts,
    threadId: threadId,
    isSelf: isSelf
  });

  // Chuẩn bị phản hồi tự động
  const autoReplyMessage = "Đã Nhận";
  const webhookUrl = "https://webhook.mooly.vn/webhook/9970c4e7-1891-401a-9182-9e084d435982";

  // Tạo payload để gửi đến webhook
  const webhookPayload: WebhookResponse = {
    message: autoReplyMessage,
    timestamp: new Date().toISOString(),
    originalMessage: {
      from: dName,
      userId: uidFrom,
      content: content,
      messageType: msgType,
      threadId: threadId,
      receivedAt: ts,
      messageId: data.msgId,
      clientMessageId: data.cliMsgId
    }
  };

  try {
    // Xử lý tin nhắn với Chatwoot nếu được bật
    let chatwootResult = null;
    if (chatwootService) {
      try {
        logger.info('Processing message with Chatwoot', {
          requestId: req.id,
          from: dName,
          userId: uidFrom
        });

        // Lấy tenantId từ middleware (đã được validate)
        const tenantId = req.tenantId!;

        chatwootResult = await chatwootService.processZaloMessage({
          from: dName,
          content: content,
          userId: uidFrom,
          messageId: data.msgId,
          timestamp: parseInt(ts),
          threadId: threadId,
          tenantId: tenantId
        });

        logger.info('Chatwoot processing successful', {
          requestId: req.id,
          contactId: chatwootResult.contact.id,
          conversationId: chatwootResult.conversation.id,
          messageId: chatwootResult.message.id
        });

      } catch (chatwootError: any) {
        logger.error('Chatwoot processing failed', {
          requestId: req.id,
          error: chatwootError.message,
          from: dName,
          userId: uidFrom
        });
        // Không throw error để không làm fail webhook
      }
    }

    // Gửi phản hồi tự động đến webhook
    logger.info('Sending auto-reply to webhook', {
      requestId: req.id,
      webhookUrl: webhookUrl,
      payload: webhookPayload
    });

    const webhookResponse = await axios.post(webhookUrl, webhookPayload, {
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Zalo-Chatbot-API/1.0.0'
      },
      timeout: 10000 // 10 seconds timeout
    });

    logger.info('Webhook response received', {
      requestId: req.id,
      status: webhookResponse.status,
      statusText: webhookResponse.statusText,
      responseData: webhookResponse.data
    });

    // Trả về phản hồi thành công
    res.status(200).json({
      success: true,
      message: 'Message received and processed successfully',
      data: {
        receivedMessage: {
          from: dName,
          content: content,
          messageId: data.msgId,
          timestamp: ts
        },
        autoReply: {
          message: autoReplyMessage,
          sentAt: new Date().toISOString(),
          webhookStatus: webhookResponse.status
        },
        webhookResponse: {
          status: webhookResponse.status,
          data: webhookResponse.data
        },
        chatwoot: chatwootResult ? {
          enabled: true,
          contactId: chatwootResult.contact.id,
          conversationId: chatwootResult.conversation.id,
          messageId: chatwootResult.message.id,
          status: 'success'
        } : {
          enabled: chatwootConfig.enabled,
          status: chatwootConfig.enabled ? 'failed' : 'disabled'
        }
      }
    });

  } catch (webhookError: any) {
    logger.error('Failed to send webhook', {
      requestId: req.id,
      error: webhookError.message,
      webhookUrl: webhookUrl,
      payload: webhookPayload
    });

    // Vẫn trả về thành công cho Zalo nhưng ghi log lỗi webhook
    res.status(200).json({
      success: true,
      message: 'Message received but failed to send auto-reply',
      data: {
        receivedMessage: {
          from: dName,
          content: content,
          messageId: data.msgId,
          timestamp: ts
        },
        autoReply: {
          message: autoReplyMessage,
          error: webhookError.message,
          sentAt: new Date().toISOString()
        }
      }
    });
  }
}));

// GET /api/zalo-webhook/test - Test endpoint để kiểm tra webhook
router.get('/test', asyncHandler(async (req: Request, res: Response) => {
  logger.info('Zalo webhook test endpoint accessed', { requestId: req.id });
  
  res.json({
    success: true,
    message: 'Zalo webhook endpoint is working',
    data: {
      endpoint: '/api/zalo-webhook',
      method: 'POST',
      expectedFormat: {
        type: 0,
        data: {
          actionId: "string",
          msgId: "string",
          cliMsgId: "string",
          msgType: "string",
          uidFrom: "string",
          idTo: "string",
          dName: "string",
          ts: "string",
          status: "number",
          content: "string",
          // ... other fields
        },
        threadId: "string",
        isSelf: "boolean"
      },
      autoReplyMessage: "Đã Nhận",
      webhookUrl: "https://webhook.mooly.vn/webhook/9970c4e7-1891-401a-9182-9e084d435982",
      timestamp: new Date().toISOString()
    }
  });
}));

// POST /api/zalo-webhook/test-send - Test gửi webhook thủ công
router.post('/test-send', asyncHandler(async (req: Request, res: Response) => {
  const { message = "Test message from API" } = req.body;
  const webhookUrl = "https://webhook.mooly.vn/webhook/9970c4e7-1891-401a-9182-9e084d435982";

  logger.info('Manual webhook test initiated', { 
    requestId: req.id, 
    message: message 
  });

  const testPayload: WebhookResponse = {
    message: message,
    timestamp: new Date().toISOString(),
    originalMessage: {
      from: "Test User",
      userId: "test-user-id",
      content: "This is a test message",
      messageType: "test",
      threadId: "test-thread-id",
      receivedAt: new Date().toISOString(),
      messageId: "test-msg-id",
      clientMessageId: "test-cli-msg-id"
    }
  };

  try {
    const webhookResponse = await axios.post(webhookUrl, testPayload, {
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Zalo-Chatbot-API/1.0.0'
      },
      timeout: 10000
    });

    logger.info('Test webhook sent successfully', {
      requestId: req.id,
      status: webhookResponse.status,
      responseData: webhookResponse.data
    });

    res.json({
      success: true,
      message: 'Test webhook sent successfully',
      data: {
        sentPayload: testPayload,
        webhookResponse: {
          status: webhookResponse.status,
          statusText: webhookResponse.statusText,
          data: webhookResponse.data
        }
      }
    });

  } catch (error: any) {
    logger.error('Test webhook failed', {
      requestId: req.id,
      error: error.message
    });

    throw new AppError(
      `Failed to send test webhook: ${error.message}`, 
      500, 
      'WEBHOOK_TEST_FAILED'
    );
  }
}));

// GET /api/zalo-webhook/chatwoot-test - Test Chatwoot connection
router.get('/chatwoot-test', asyncHandler(async (req: Request, res: Response) => {
  logger.info('Chatwoot connection test endpoint accessed', { requestId: req.id });

  if (!chatwootService) {
    return res.json({
      success: false,
      message: 'Chatwoot integration is disabled or misconfigured',
      data: {
        enabled: chatwootConfig.enabled,
        config: {
          baseUrl: chatwootConfig.baseUrl,
          accountId: chatwootConfig.accountId,
          inboxId: chatwootConfig.inboxId,
          hasApiToken: !!chatwootConfig.apiAccessToken
        },
        validation: validateChatwootConfig()
      }
    });
  }

  try {
    const connectionTest = await chatwootService.testConnection();

    res.json({
      success: connectionTest,
      message: connectionTest ? 'Chatwoot connection successful' : 'Chatwoot connection failed',
      data: {
        enabled: true,
        config: {
          baseUrl: chatwootConfig.baseUrl,
          accountId: chatwootConfig.accountId,
          inboxId: chatwootConfig.inboxId,
          hasApiToken: !!chatwootConfig.apiAccessToken
        },
        connectionStatus: connectionTest ? 'connected' : 'failed',
        validation: validateChatwootConfig()
      }
    });

  } catch (error: any) {
    logger.error('Chatwoot connection test failed', {
      requestId: req.id,
      error: error.message
    });

    res.json({
      success: false,
      message: 'Chatwoot connection test failed',
      data: {
        enabled: true,
        error: error.message,
        config: {
          baseUrl: chatwootConfig.baseUrl,
          accountId: chatwootConfig.accountId,
          inboxId: chatwootConfig.inboxId,
          hasApiToken: !!chatwootConfig.apiAccessToken
        },
        validation: validateChatwootConfig()
      }
    });
  }
}));

// POST /api/zalo-webhook/chatwoot-test-contact - Test tạo contact
router.post('/chatwoot-test-contact', asyncHandler(async (req: Request, res: Response) => {
  logger.info('Chatwoot contact creation test endpoint accessed', { requestId: req.id });

  if (!chatwootService) {
    return res.json({
      success: false,
      message: 'Chatwoot integration is disabled or misconfigured',
      data: {
        enabled: chatwootConfig.enabled
      }
    });
  }

  const { name = 'Test User', identifier = 'test-user-' + Date.now() } = req.body;

  try {
    logger.info('Testing contact creation', {
      requestId: req.id,
      name,
      identifier
    });

    const { contact, sourceId } = await chatwootService.createOrFindContact({
      name,
      identifier,
      phone_number: '+***********',
      email: '<EMAIL>'
    });

    res.json({
      success: true,
      message: 'Contact created/found successfully',
      data: {
        contact: {
          id: contact.id,
          name: contact.name,
          email: contact.email,
          phone_number: contact.phone_number
        },
        sourceId,
        contactInboxes: contact.contact_inboxes?.length || 0
      }
    });

  } catch (error: any) {
    logger.error('Chatwoot contact creation test failed', {
      requestId: req.id,
      error: error.message
    });

    res.json({
      success: false,
      message: 'Contact creation test failed',
      data: {
        error: error.message,
        name,
        identifier
      }
    });
  }
}));

// POST /api/zalo-webhook/chatwoot-test-message - Test tạo message hoàn chỉnh
router.post('/chatwoot-test-message', validateTenant, asyncHandler(async (req: Request, res: Response) => {
  logger.info('Chatwoot message creation test endpoint accessed', { requestId: req.id });

  if (!chatwootService) {
    return res.json({
      success: false,
      message: 'Chatwoot integration is disabled or misconfigured'
    });
  }

  const {
    name = 'Test User',
    identifier = 'test-user-' + Date.now(),
    content = 'This is a test message from API',
    threadId = 'test-thread-' + Date.now()
  } = req.body;

  try {
    logger.info('Testing full message flow', {
      requestId: req.id,
      name,
      identifier,
      content,
      threadId
    });

    // Lấy tenantId từ middleware (đã được validate)
    const tenantId = req.tenantId!;

    const result = await chatwootService.processZaloMessage({
      from: name,
      content,
      userId: identifier,
      messageId: 'test-msg-' + Date.now(),
      timestamp: Date.now(),
      threadId: threadId,
      tenantId: tenantId
    });

    res.json({
      success: true,
      message: 'Message flow test successful',
      data: {
        contact: {
          id: result.contact.id,
          name: result.contact.name
        },
        conversation: {
          id: result.conversation.id,
          status: result.conversation.status
        },
        message: {
          id: result.message.id,
          content: result.message.content,
          message_type: result.message.message_type
        }
      }
    });

  } catch (error: any) {
    logger.error('Chatwoot message creation test failed', {
      requestId: req.id,
      error: error.message
    });

    res.json({
      success: false,
      message: 'Message flow test failed',
      data: {
        error: error.message,
        name,
        identifier,
        content,
        threadId
      }
    });
  }
}));

// GET /api/zalo-webhook/chatwoot-config - Hiển thị cấu hình hiện tại
router.get('/chatwoot-config', asyncHandler(async (req: Request, res: Response) => {
  logger.info('Chatwoot config endpoint accessed', { requestId: req.id });

  const configStatus = {
    enabled: chatwootConfig.enabled,
    baseUrl: chatwootConfig.baseUrl,
    accountId: chatwootConfig.accountId,
    inboxId: chatwootConfig.inboxId,
    hasApiToken: !!chatwootConfig.apiAccessToken,
    validation: validateChatwootConfig(),
    serviceInitialized: !!chatwootService
  };

  res.json({
    success: true,
    message: 'Chatwoot configuration status',
    data: configStatus
  });
}));

export default router;
