import axios, { AxiosInstance } from 'axios';
import { logger } from '../utils/logger';

// Declare Node.js globals
declare const process: any;

// Interfaces cho Chatwoot API
interface ChatwootConfig {
  baseUrl: string;
  apiAccessToken: string;
  accountId: number;
  inboxId: number;
}

interface ChatwootContact {
  id?: number;
  email?: string;
  name: string;
  phone_number?: string;
  thumbnail?: string;
  additional_attributes?: any;
  contact_inboxes?: ContactInbox[];
  availability_status?: string;
}

interface ContactInbox {
  source_id: string;
  inbox: {
    id: number;
    name: string;
    channel_type: string;
    [key: string]: any;
  };
}

interface ChatwootConversation {
  id: number;
  status?: string;
  inbox_id?: number;
  contact_id?: number;
  [key: string]: any;
}

interface ChatwootMessage {
  id: number;
  content: string;
  message_type: 'incoming' | 'outgoing';
  content_type?: string;
  conversation_id: number;
  inbox_id: number;
  sender: {
    id: number;
    name: string;
    type: 'contact' | 'agent';
  };
  created_at: string;
  [key: string]: any;
}

export class ChatwootService {
  private config: ChatwootConfig;
  private apiClient: AxiosInstance;

  constructor(config: ChatwootConfig) {
    this.config = config;
    this.apiClient = axios.create({
      baseURL: this.config.baseUrl,
      headers: {
        'api_access_token': this.config.apiAccessToken,
        'Content-Type': 'application/json'
      },
      timeout: 10000
    });
  }

  /**
   * Tìm contact hiện có theo identifier
   */
  async findContactByIdentifier(identifier: string): Promise<ChatwootContact | null> {
    try {
      logger.info('Searching for existing contact', { identifier });

      // Thử tìm bằng search API trước
      const searchResponse = await this.apiClient.get(`/api/v1/accounts/${this.config.accountId}/contacts/search`, {
        params: {
          q: identifier
        }
      });

      const searchContacts = searchResponse.data.payload || [];
      logger.info('Search API response', {
        contactsFound: searchContacts.length,
        searchQuery: identifier
      });

      // Tìm contact có zalo_uid khớp trong kết quả search
      let existingContact = searchContacts.find((contact: any) =>
        contact.additional_attributes?.zalo_uid === identifier
      );

      if (existingContact) {
        logger.info('Found existing contact via search', {
          contactId: existingContact.id,
          name: existingContact.name,
          zalouId: existingContact.additional_attributes?.zalo_uid
        });
        return existingContact;
      }

      // Nếu search không tìm thấy, thử list tất cả contacts với filter
      logger.info('Search API did not find contact, trying list API');

      const listResponse = await this.apiClient.get(`/api/v1/accounts/${this.config.accountId}/contacts`, {
        params: {
          page: 1,
          per_page: 50 // Lấy 50 contact gần nhất
        }
      });

      const listContacts = listResponse.data.payload || [];
      logger.info('List API response', {
        contactsFound: listContacts.length
      });

      // Tìm contact có zalo_uid khớp trong danh sách
      existingContact = listContacts.find((contact: any) =>
        contact.additional_attributes?.zalo_uid === identifier
      );

      if (existingContact) {
        logger.info('Found existing contact via list', {
          contactId: existingContact.id,
          name: existingContact.name,
          zalouId: existingContact.additional_attributes?.zalo_uid
        });
        return existingContact;
      }

      logger.info('No existing contact found in both search and list');
      return null;

    } catch (error: any) {
      logger.error('Failed to search contact', {
        error: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        responseData: error.response?.data,
        identifier
      });
      return null;
    }
  }

  /**
   * Tạo hoặc tìm contact trong Chatwoot
   */
  async createOrFindContact(contactData: {
    name: string;
    phone_number?: string;
    email?: string;
    identifier: string; // Unique identifier từ Zalo (uidFrom)
    threadId?: string; // Thread ID từ Zalo để tạo source_id duy nhất
  }): Promise<{ contact: ChatwootContact; sourceId: string }> {
    try {
      logger.info('Creating/finding contact in Chatwoot', {
        name: contactData.name,
        identifier: contactData.identifier
      });

      // Tìm contact hiện có trước
      let contact = await this.findContactByIdentifier(contactData.identifier);

      if (!contact) {
        // Tạo contact mới với inbox_id để tự động tạo contact_inbox
        const payload = {
          name: contactData.name,
          phone_number: contactData.phone_number,
          email: contactData.email,
          inbox_id: this.config.inboxId,
          additional_attributes: {
            zalo_uid: contactData.identifier,
            source: 'zalo_webhook'
          }
        };

        logger.info('Creating new contact with payload', {
          payload: JSON.stringify(payload, null, 2)
        });

        const response = await this.apiClient.post(`/api/v1/accounts/${this.config.accountId}/contacts`, payload);

        // Chatwoot API trả về dạng { payload: { contact: {...} } }
        const responseData = response.data;
        contact = responseData.payload?.contact || responseData.contact || responseData;

        logger.info('New contact created successfully', {
          contactId: contact?.id,
          contactInboxes: contact?.contact_inboxes?.length,
          responseStructure: Object.keys(responseData),
          contactData: JSON.stringify(contact, null, 2)
        });

        // Kiểm tra xem contact có được tạo đúng không
        if (!contact || !contact.id) {
          logger.error('Contact creation failed - invalid response structure', {
            responseData: JSON.stringify(responseData, null, 2)
          });
          throw new Error('Contact creation failed - no contact ID returned');
        }
      } else {
        logger.info('Using existing contact', {
          contactId: contact.id,
          contactInboxes: contact.contact_inboxes?.length
        });
      }

      // Lấy source_id từ contact_inbox
      const contactInbox = contact.contact_inboxes?.find(
        ci => ci.inbox.id === this.config.inboxId
      );

      if (!contactInbox) {
        logger.info('No contact inbox found for this inbox', {
          contactId: contact.id,
          expectedInboxId: this.config.inboxId,
          availableInboxes: contact.contact_inboxes?.map(ci => ci.inbox.id)
        });

        // Tạo contact_inbox mới bằng cách sử dụng API riêng biệt
        try {
          const contactInboxPayload = {
            contact_id: contact.id,
            inbox_id: this.config.inboxId,
            source_id: contactData.threadId ? `zalo_thread_${contactData.threadId}` : `zalo_${contactData.identifier}_${Date.now()}`
          };

          logger.info('Creating contact inbox with payload', {
            payload: JSON.stringify(contactInboxPayload, null, 2)
          });

          const contactInboxResponse = await this.apiClient.post(
            `/api/v1/accounts/${this.config.accountId}/contact_inboxes`,
            contactInboxPayload
          );

          const newContactInbox = contactInboxResponse.data;

          logger.info('Contact inbox created successfully', {
            contactInboxId: newContactInbox.id,
            sourceId: newContactInbox.source_id
          });

          return {
            contact,
            sourceId: newContactInbox.source_id
          };

        } catch (contactInboxError: any) {
          logger.error('Failed to create contact inbox via API', {
            error: contactInboxError.message,
            status: contactInboxError.response?.status,
            statusText: contactInboxError.response?.statusText,
            responseData: contactInboxError.response?.data,
            contactId: contact.id,
            inboxId: this.config.inboxId
          });

          // Fallback: tạo source_id tự động
          const fallbackSourceId = contactData.threadId ? `zalo_thread_${contactData.threadId}` : `zalo_${contactData.identifier}_${Date.now()}`;

          logger.warn('Using fallback source_id', {
            sourceId: fallbackSourceId,
            contactId: contact.id
          });

          return {
            contact,
            sourceId: fallbackSourceId
          };
        }
      }

      return {
        contact,
        sourceId: contactInbox.source_id
      };

    } catch (error: any) {
      logger.error('Failed to create/find contact', {
        error: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        responseData: error.response?.data,
        requestData: {
          name: contactData.name,
          identifier: contactData.identifier,
          inboxId: this.config.inboxId
        }
      });
      throw error;
    }
  }

  /**
   * Tạo conversation mới
   */
  async createConversation(sourceId: string): Promise<ChatwootConversation> {
    try {
      logger.info('Creating conversation in Chatwoot', { sourceId });

      const payload = {
        source_id: sourceId,
        inbox_id: this.config.inboxId
      };

      const response = await this.apiClient.post(`/api/v1/accounts/${this.config.accountId}/conversations`, payload);
      const conversation: ChatwootConversation = response.data;

      logger.info('Conversation created successfully', {
        conversationId: conversation.id
      });

      return conversation;

    } catch (error: any) {
      logger.error('Failed to create conversation', {
        error: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        responseData: error.response?.data,
        requestData: {
          sourceId,
          inboxId: this.config.inboxId
        }
      });
      throw error;
    }
  }

  /**
   * Tạo message trong conversation
   */
  async createMessage(
    conversationId: number,
    content: string,
    messageType: 'incoming' | 'outgoing' = 'incoming'
  ): Promise<ChatwootMessage> {
    try {
      logger.info('Creating message in Chatwoot', {
        conversationId,
        messageType,
        contentLength: content.length
      });

      const payload = {
        content: content,
        message_type: messageType,
        private: false
      };

      const response = await this.apiClient.post(
        `/api/v1/accounts/${this.config.accountId}/conversations/${conversationId}/messages`,
        payload
      );
      const message: ChatwootMessage = response.data;

      logger.info('Message created successfully', {
        messageId: message.id,
        conversationId: message.conversation_id
      });

      return message;

    } catch (error: any) {
      logger.error('Failed to create message', {
        error: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        responseData: error.response?.data,
        requestData: {
          conversationId,
          messageType,
          contentLength: content.length
        }
      });
      throw error;
    }
  }

  /**
   * Tìm conversation hiện có theo source_id (threadId từ Zalo)
   */
  async findExistingConversationBySourceId(sourceId: string): Promise<ChatwootConversation | null> {
    try {
      logger.info('Finding existing conversation by source_id', { sourceId });

      const response = await this.apiClient.get(`/api/v1/accounts/${this.config.accountId}/conversations`, {
        params: {
          inbox_id: this.config.inboxId,
          status: 'open'
        }
      });

      const responseData = response.data;
      let conversations = [];
      if (responseData.data && responseData.data.payload) {
        conversations = responseData.data.payload;
      } else if (responseData.payload) {
        conversations = responseData.payload;
      }

      if (!Array.isArray(conversations)) {
        return null;
      }

      // Tìm conversation có source_id khớp
      const existingConversation = conversations.find((conv: any) => {
        const conversationSourceId = conv.contact_inbox?.source_id;
        const isMatching = conversationSourceId === sourceId;

        logger.info('Checking conversation by source_id', {
          conversationId: conv.id,
          conversationSourceId,
          targetSourceId: sourceId,
          isMatching
        });

        return isMatching;
      });

      if (existingConversation) {
        logger.info('Found existing conversation by source_id', {
          conversationId: existingConversation.id,
          sourceId: existingConversation.contact_inbox?.source_id
        });
        return existingConversation;
      }

      logger.info('No existing conversation found by source_id', { sourceId });
      return null;

    } catch (error: any) {
      logger.error('Failed to find conversation by source_id', {
        error: error.message,
        sourceId
      });
      return null;
    }
  }

  /**
   * Tìm conversation hiện có của contact
   */
  async findExistingConversation(contactId: number): Promise<ChatwootConversation | null> {
    try {
      logger.info('Finding existing conversations', { contactId });

      const response = await this.apiClient.get(`/api/v1/accounts/${this.config.accountId}/conversations`, {
        params: {
          inbox_id: this.config.inboxId,
          status: 'open'
        }
      });

      // Xử lý cấu trúc response từ Chatwoot API
      const responseData = response.data;

      // Response structure: { data: { payload: [...], meta: {...} } }
      let conversations = [];
      if (responseData.data && responseData.data.payload) {
        conversations = responseData.data.payload;
      } else if (responseData.payload) {
        conversations = responseData.payload;
      } else if (Array.isArray(responseData)) {
        conversations = responseData;
      }

      logger.info('Conversations response structure', {
        responseKeys: Object.keys(responseData),
        dataKeys: responseData.data ? Object.keys(responseData.data) : 'no data key',
        conversationsType: Array.isArray(conversations) ? 'array' : typeof conversations,
        conversationsLength: Array.isArray(conversations) ? conversations.length : 'not array',
        conversationsPreview: Array.isArray(conversations) ? conversations.slice(0, 2).map(c => ({
          id: c.id,
          contact_id: c.meta?.sender?.id,
          inbox_id: c.inbox_id,
          status: c.status
        })) : 'not array'
      });

      // Đảm bảo conversations là array
      if (!Array.isArray(conversations)) {
        logger.warn('Conversations is not an array after processing', {
          conversationsType: typeof conversations,
          conversationsValue: conversations
        });
        return null;
      }

      // Tìm conversation của contact này trong inbox hiện tại
      const existingConversation = conversations.find((conv: any) => {
        const isMatchingContact = conv.meta?.sender?.id === contactId;
        const isMatchingInbox = conv.inbox_id === this.config.inboxId;
        const isOpenStatus = conv.status === 'open' || conv.status === 'pending';

        logger.info('Checking conversation', {
          conversationId: conv.id,
          contactId: conv.meta?.sender?.id,
          inboxId: conv.inbox_id,
          status: conv.status,
          isMatchingContact,
          isMatchingInbox,
          isOpenStatus,
          targetContactId: contactId,
          targetInboxId: this.config.inboxId
        });

        return isMatchingContact && isMatchingInbox && isOpenStatus;
      });

      if (existingConversation) {
        logger.info('Found existing conversation', {
          conversationId: existingConversation.id,
          contactId: existingConversation.meta?.sender?.id,
          inboxId: existingConversation.inbox_id,
          status: existingConversation.status
        });
        return existingConversation;
      }

      logger.info('No existing conversation found for contact', {
        contactId,
        inboxId: this.config.inboxId,
        totalConversations: conversations.length
      });
      return null;

    } catch (error: any) {
      logger.error('Failed to find existing conversation', {
        error: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        responseData: error.response?.data,
        requestData: {
          contactId,
          inboxId: this.config.inboxId
        }
      });
      return null;
    }
  }

  /**
   * Xử lý tin nhắn từ Zalo - tạo contact, conversation và message
   */
  async processZaloMessage(messageData: {
    from: string;
    content: string;
    userId: string;
    messageId: string;
    timestamp: string;
    threadId?: string; // Thêm threadId để tạo source_id duy nhất
  }): Promise<{
    contact: ChatwootContact;
    conversation: ChatwootConversation;
    message: ChatwootMessage;
  }> {
    try {
      logger.info('Processing Zalo message for Chatwoot', {
        from: messageData.from,
        userId: messageData.userId,
        messageId: messageData.messageId
      });

      // 1. Tạo hoặc tìm contact
      const { contact, sourceId } = await this.createOrFindContact({
        name: messageData.from,
        identifier: messageData.userId,
        threadId: messageData.threadId, // Truyền threadId để tạo source_id duy nhất
        phone_number: undefined, // Có thể thêm nếu có
        email: undefined // Có thể thêm nếu có
      });

      // 2. Tìm conversation hiện có hoặc tạo mới
      let conversation: ChatwootConversation | null = null;

      try {
        // Ưu tiên tìm theo source_id (threadId) trước
        logger.info('Looking for existing conversation by source_id first', { sourceId });
        conversation = await this.findExistingConversationBySourceId(sourceId);

        if (!conversation) {
          // Nếu không tìm thấy theo source_id, tìm theo contact_id
          logger.info('No conversation found by source_id, trying by contact_id');
          conversation = await this.findExistingConversation(contact.id!);
        }

        if (!conversation) {
          logger.info('No existing conversation found, creating new one');
          conversation = await this.createConversation(sourceId);
        } else {
          logger.info('Using existing conversation', {
            conversationId: conversation.id,
            foundBy: conversation.contact_inbox?.source_id === sourceId ? 'source_id' : 'contact_id'
          });
        }
      } catch (findError: any) {
        logger.warn('Failed to find existing conversation, creating new one', {
          error: findError.message
        });
        conversation = await this.createConversation(sourceId);
      }

      // 3. Tạo message
      const message = await this.createMessage(
        conversation.id,
        messageData.content,
        'incoming'
      );

      logger.info('Zalo message processed successfully', {
        contactId: contact.id,
        conversationId: conversation.id,
        messageId: message.id
      });

      return {
        contact,
        conversation,
        message
      };

    } catch (error: any) {
      logger.error('Failed to process Zalo message', {
        error: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        responseData: error.response?.data,
        messageData: {
          from: messageData.from,
          userId: messageData.userId,
          messageId: messageData.messageId,
          contentLength: messageData.content.length
        }
      });
      throw error;
    }
  }

  /**
   * Gửi message từ agent (outgoing)
   */
  async sendAgentMessage(
    conversationId: number,
    content: string
  ): Promise<ChatwootMessage> {
    return this.createMessage(conversationId, content, 'outgoing');
  }

  /**
   * Test connection với Chatwoot
   */
  async testConnection(): Promise<boolean> {
    try {
      logger.info('Testing Chatwoot connection');
      
      const response = await this.apiClient.get('/api/v1/profile');
      
      logger.info('Chatwoot connection successful', {
        profile: response.data
      });
      
      return true;
    } catch (error: any) {
      logger.error('Chatwoot connection failed', {
        error: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        responseData: error.response?.data,
        config: {
          baseUrl: this.config.baseUrl,
          hasToken: !!this.config.apiAccessToken,
          accountId: this.config.accountId
        }
      });
      return false;
    }
  }
}
