import { logger } from '@/utils/logger';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Đ<PERSON><PERSON> bảo biến môi trường được load
dotenv.config();

// Cấu hình Supabase client
const supabaseUrl = process.env.SUPABASE_URL || 'https://vhduizefoibsipsiraqf.supabase.co';
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZoZHVpemVmb2lic2lwc2lyYXFmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUxOTU2NzAsImV4cCI6MjA2MDc3MTY3MH0.p3ioFOmyQ4sA6XMn5SrmP3Ub1p00agSWewyCb3CGjwg';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY || '';

// Tạo và export Supabase client thông thường (giữ lại để tương thích với code hiện tại)
export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Tạo và export Supabase admin client với service role key
// Service role key có quyền bypass Row Level Security (RLS)
export const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Kiểm tra xem service key có được cấu hình hay không
export const isServiceKeyConfigured = (): boolean => {
  return !!supabaseServiceKey && supabaseServiceKey.length > 0;
};


// Kiểm tra kết nối Supabase
export const checkSupabaseConnection = async () => {
  try {

    // Nếu kết nối PostgreSQL thất bại, thử kết nối qua Supabase
    const { data, error } = await supabase.from('products').select('id').limit(1);

    if (error) {
      logger.error('Lỗi kết nối Supabase:', error.message);
      return {
        success: false,
        message: `Lỗi kết nối Supabase: ${error.message}`,
      };
    }

    logger.info('Kết nối Supabase thành công');
    return {
      success: true,
      message: 'Kết nối Supabase thành công',
    };
  } catch (error: any) {
    logger.error('Lỗi kết nối Supabase:', error);
    return {
      success: false,
      message: `Lỗi kết nối Supabase: ${error?.message || 'Lỗi không xác định'}`,
    };
  }
};

// Kiểm tra kết nối Supabase Admin (với service role key)
export const checkSupabaseAdminConnection = async () => {
  try {
    // Kiểm tra xem service key có được cấu hình hay không
    if (!isServiceKeyConfigured()) {
      return {
        success: false,
        message: 'Supabase Service Key chưa được cấu hình',
      };
    }

    // Thử kết nối với quyền admin
    const { data, error } = await supabaseAdmin.from('storage').select('id').limit(1);

    if (error) {
      logger.error('Lỗi kết nối Supabase Admin:', error.message);
      return {
        success: false,
        message: `Lỗi kết nối Supabase Admin: ${error.message}`,
      };
    }

    logger.info('Kết nối Supabase Admin thành công');
    return {
      success: true,
      message: 'Kết nối Supabase Admin thành công',
    };
  } catch (error: any) {
    logger.error('Lỗi kết nối Supabase Admin:', error);
    return {
      success: false,
      message: `Lỗi kết nối Supabase Admin: ${error?.message || 'Lỗi không xác định'}`,
    };
  }
};
