import { Router, Request, Response } from 'express';
import { asyncHand<PERSON> } from '../middleware/errorHandler';
import { AppError } from '../middleware/errorHandler';
import { validateTenant } from '../middleware/tenantMiddleware';
import { logger } from '../utils/logger';
import { zaloChatwootMappingService } from '../services/ZaloChatwootMappingService';

const router = Router();

// Middleware để validate tenant cho tất cả routes
router.use(validateTenant);

// GET /api/zalo-chatwoot-mapping - Lấy danh sách mapping
router.get('/', asyncHandler(async (req: Request, res: Response) => {
  const tenantId = req.tenantId!;
  
  logger.info('Getting Zalo-Chatwoot mappings', {
    tenantId,
    requestId: req.id
  });

  try {
    // Lấy thống kê
    const stats = await zaloChatwootMappingService.getMappingStats(tenantId);

    res.json({
      success: true,
      data: {
        stats
      }
    });
  } catch (error: any) {
    logger.error('Failed to get mappings', {
      tenantId,
      error: error.message,
      requestId: req.id
    });
    throw new AppError('Failed to get mappings', 500, 'GET_MAPPINGS_ERROR');
  }
}));

// GET /api/zalo-chatwoot-mapping/by-thread/:threadId - Lấy mapping theo thread ID
router.get('/by-thread/:threadId', asyncHandler(async (req: Request, res: Response) => {
  const tenantId = req.tenantId!;
  const { threadId } = req.params;
  
  logger.info('Getting mapping by thread ID', {
    tenantId,
    threadId,
    requestId: req.id
  });

  try {
    const mapping = await zaloChatwootMappingService.findByZaloThreadId(tenantId, threadId);

    if (!mapping) {
      throw new AppError('Mapping not found', 404, 'MAPPING_NOT_FOUND');
    }

    res.json({
      success: true,
      data: mapping
    });
  } catch (error: any) {
    if (error instanceof AppError) {
      throw error;
    }
    
    logger.error('Failed to get mapping by thread ID', {
      tenantId,
      threadId,
      error: error.message,
      requestId: req.id
    });
    throw new AppError('Failed to get mapping', 500, 'GET_MAPPING_ERROR');
  }
}));

// GET /api/zalo-chatwoot-mapping/by-user/:userId - Lấy mapping theo user ID
router.get('/by-user/:userId', asyncHandler(async (req: Request, res: Response) => {
  const tenantId = req.tenantId!;
  const { userId } = req.params;
  
  logger.info('Getting mappings by user ID', {
    tenantId,
    userId,
    requestId: req.id
  });

  try {
    const mappings = await zaloChatwootMappingService.findByZaloUserId(tenantId, userId);

    res.json({
      success: true,
      data: mappings,
      count: mappings.length
    });
  } catch (error: any) {
    logger.error('Failed to get mappings by user ID', {
      tenantId,
      userId,
      error: error.message,
      requestId: req.id
    });
    throw new AppError('Failed to get mappings', 500, 'GET_MAPPINGS_ERROR');
  }
}));

// GET /api/zalo-chatwoot-mapping/by-contact/:contactId - Lấy mapping theo contact ID
router.get('/by-contact/:contactId', asyncHandler(async (req: Request, res: Response) => {
  const tenantId = req.tenantId!;
  const contactId = parseInt(req.params.contactId);
  
  if (isNaN(contactId)) {
    throw new AppError('Invalid contact ID', 400, 'INVALID_CONTACT_ID');
  }
  
  logger.info('Getting mappings by contact ID', {
    tenantId,
    contactId,
    requestId: req.id
  });

  try {
    const mappings = await zaloChatwootMappingService.findByChatwootContactId(tenantId, contactId);

    res.json({
      success: true,
      data: mappings,
      count: mappings.length
    });
  } catch (error: any) {
    logger.error('Failed to get mappings by contact ID', {
      tenantId,
      contactId,
      error: error.message,
      requestId: req.id
    });
    throw new AppError('Failed to get mappings', 500, 'GET_MAPPINGS_ERROR');
  }
}));

// PUT /api/zalo-chatwoot-mapping/:threadId/deactivate - Deactivate mapping
router.put('/:threadId/deactivate', asyncHandler(async (req: Request, res: Response) => {
  const tenantId = req.tenantId!;
  const { threadId } = req.params;
  
  logger.info('Deactivating mapping', {
    tenantId,
    threadId,
    requestId: req.id
  });

  try {
    await zaloChatwootMappingService.deactivateMapping(tenantId, threadId);

    res.json({
      success: true,
      message: 'Mapping deactivated successfully'
    });
  } catch (error: any) {
    logger.error('Failed to deactivate mapping', {
      tenantId,
      threadId,
      error: error.message,
      requestId: req.id
    });
    throw new AppError('Failed to deactivate mapping', 500, 'DEACTIVATE_MAPPING_ERROR');
  }
}));

// PUT /api/zalo-chatwoot-mapping/:threadId - Cập nhật mapping
router.put('/:threadId', asyncHandler(async (req: Request, res: Response) => {
  const tenantId = req.tenantId!;
  const { threadId } = req.params;
  const updateData = req.body;
  
  logger.info('Updating mapping', {
    tenantId,
    threadId,
    updateData,
    requestId: req.id
  });

  try {
    const updatedMapping = await zaloChatwootMappingService.updateMapping(
      tenantId,
      threadId,
      updateData
    );

    res.json({
      success: true,
      data: updatedMapping,
      message: 'Mapping updated successfully'
    });
  } catch (error: any) {
    logger.error('Failed to update mapping', {
      tenantId,
      threadId,
      updateData,
      error: error.message,
      requestId: req.id
    });
    throw new AppError('Failed to update mapping', 500, 'UPDATE_MAPPING_ERROR');
  }
}));

// GET /api/zalo-chatwoot-mapping/stats - Lấy thống kê chi tiết
router.get('/stats', asyncHandler(async (req: Request, res: Response) => {
  const tenantId = req.tenantId!;
  
  logger.info('Getting detailed mapping stats', {
    tenantId,
    requestId: req.id
  });

  try {
    const stats = await zaloChatwootMappingService.getMappingStats(tenantId);

    res.json({
      success: true,
      data: {
        stats,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error: any) {
    logger.error('Failed to get mapping stats', {
      tenantId,
      error: error.message,
      requestId: req.id
    });
    throw new AppError('Failed to get stats', 500, 'GET_STATS_ERROR');
  }
}));

export default router;
