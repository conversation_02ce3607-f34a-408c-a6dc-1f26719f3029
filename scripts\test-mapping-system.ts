import { zaloChatwootMappingService } from '../src/services/ZaloChatwootMappingService';
import { tenantService } from '../src/services/TenantService';
import { logger } from '../src/utils/logger';

// Test data
const TEST_DATA = {
  tenantId: '', // Will be set from default tenant
  zaloThreadId: 'test-thread-' + Date.now(),
  zaloUserId: 'test-user-' + Date.now(),
  zaloUserName: 'Test User',
  chatwootContactId: 12345,
  chatwootConversationId: 67890,
  chatwootInboxId: 1,
  chatwootSourceId: 'zalo_test_' + Date.now()
};

async function testMappingSystem() {
  try {
    console.log('🚀 Starting Zalo-Chatwoot Mapping System Test...\n');

    // 1. Get default tenant
    console.log('1. Getting default tenant...');
    const defaultTenant = await tenantService.getDefaultTenant();
    if (!defaultTenant) {
      throw new Error('No default tenant found. Please create a tenant first.');
    }
    
    TEST_DATA.tenantId = defaultTenant.id;
    console.log(`✅ Using tenant: ${defaultTenant.name} (${defaultTenant.id})\n`);

    // 2. Test creating mapping
    console.log('2. Creating new mapping...');
    const newMapping = await zaloChatwootMappingService.createMapping({
      tenant_id: TEST_DATA.tenantId,
      zalo_thread_id: TEST_DATA.zaloThreadId,
      zalo_user_id: TEST_DATA.zaloUserId,
      zalo_user_name: TEST_DATA.zaloUserName,
      chatwoot_contact_id: TEST_DATA.chatwootContactId,
      chatwoot_conversation_id: TEST_DATA.chatwootConversationId,
      chatwoot_inbox_id: TEST_DATA.chatwootInboxId,
      chatwoot_source_id: TEST_DATA.chatwootSourceId,
      metadata: {
        test: true,
        created_by: 'test-script'
      }
    });
    console.log(`✅ Created mapping: ${newMapping.id}\n`);

    // 3. Test finding by thread ID
    console.log('3. Finding mapping by thread ID...');
    const foundByThread = await zaloChatwootMappingService.findByZaloThreadId(
      TEST_DATA.tenantId,
      TEST_DATA.zaloThreadId
    );
    if (!foundByThread) {
      throw new Error('Mapping not found by thread ID');
    }
    console.log(`✅ Found mapping by thread ID: ${foundByThread.id}\n`);

    // 4. Test finding by user ID
    console.log('4. Finding mappings by user ID...');
    const foundByUser = await zaloChatwootMappingService.findByZaloUserId(
      TEST_DATA.tenantId,
      TEST_DATA.zaloUserId
    );
    if (foundByUser.length === 0) {
      throw new Error('No mappings found by user ID');
    }
    console.log(`✅ Found ${foundByUser.length} mapping(s) by user ID\n`);

    // 5. Test finding by contact ID
    console.log('5. Finding mappings by contact ID...');
    const foundByContact = await zaloChatwootMappingService.findByChatwootContactId(
      TEST_DATA.tenantId,
      TEST_DATA.chatwootContactId
    );
    if (foundByContact.length === 0) {
      throw new Error('No mappings found by contact ID');
    }
    console.log(`✅ Found ${foundByContact.length} mapping(s) by contact ID\n`);

    // 6. Test updating mapping
    console.log('6. Updating mapping...');
    const updatedMapping = await zaloChatwootMappingService.updateMapping(
      TEST_DATA.tenantId,
      TEST_DATA.zaloThreadId,
      {
        zalo_user_name: 'Updated Test User',
        metadata: {
          test: true,
          updated_by: 'test-script',
          updated_at: new Date().toISOString()
        }
      }
    );
    console.log(`✅ Updated mapping: ${updatedMapping.zalo_user_name}\n`);

    // 7. Test incrementing message count
    console.log('7. Incrementing message count...');
    await zaloChatwootMappingService.incrementMessageCount(
      TEST_DATA.tenantId,
      TEST_DATA.zaloThreadId
    );
    
    // Verify increment
    const afterIncrement = await zaloChatwootMappingService.findByZaloThreadId(
      TEST_DATA.tenantId,
      TEST_DATA.zaloThreadId
    );
    console.log(`✅ Message count incremented to: ${afterIncrement?.message_count}\n`);

    // 8. Test getting stats
    console.log('8. Getting mapping stats...');
    const stats = await zaloChatwootMappingService.getMappingStats(TEST_DATA.tenantId);
    console.log(`✅ Stats - Total: ${stats.total}, Active: ${stats.active}, Messages: ${stats.totalMessages}\n`);

    // 9. Test deactivating mapping
    console.log('9. Deactivating mapping...');
    await zaloChatwootMappingService.deactivateMapping(
      TEST_DATA.tenantId,
      TEST_DATA.zaloThreadId
    );
    
    // Verify deactivation
    const deactivated = await zaloChatwootMappingService.findByZaloThreadId(
      TEST_DATA.tenantId,
      TEST_DATA.zaloThreadId
    );
    if (deactivated) {
      throw new Error('Mapping should not be found after deactivation (active filter)');
    }
    console.log(`✅ Mapping deactivated successfully\n`);

    // 10. Test stats after deactivation
    console.log('10. Getting stats after deactivation...');
    const finalStats = await zaloChatwootMappingService.getMappingStats(TEST_DATA.tenantId);
    console.log(`✅ Final Stats - Total: ${finalStats.total}, Active: ${finalStats.active}, Inactive: ${finalStats.inactive}\n`);

    console.log('🎉 All tests passed successfully!');
    console.log('\n📊 Test Summary:');
    console.log(`- Tenant: ${defaultTenant.name}`);
    console.log(`- Thread ID: ${TEST_DATA.zaloThreadId}`);
    console.log(`- User ID: ${TEST_DATA.zaloUserId}`);
    console.log(`- Contact ID: ${TEST_DATA.chatwootContactId}`);
    console.log(`- Final message count: ${afterIncrement?.message_count || 0}`);

  } catch (error: any) {
    console.error('❌ Test failed:', error.message);
    logger.error('Mapping system test failed', {
      error: error.message,
      stack: error.stack,
      testData: TEST_DATA
    });
    process.exit(1);
  }
}

// Test tenant validation
async function testTenantValidation() {
  try {
    console.log('\n🔐 Testing tenant validation...');

    // Test invalid tenant ID
    try {
      await zaloChatwootMappingService.findByZaloThreadId('invalid-tenant-id', 'test-thread');
      throw new Error('Should have failed with invalid tenant ID');
    } catch (error: any) {
      if (error.message.includes('invalid input syntax for type uuid')) {
        console.log('✅ Invalid tenant ID properly rejected');
      } else {
        throw error;
      }
    }

    // Test non-existent tenant ID
    const fakeUuid = '00000000-0000-0000-0000-000000000000';
    const result = await zaloChatwootMappingService.findByZaloThreadId(fakeUuid, 'test-thread');
    if (result === null) {
      console.log('✅ Non-existent tenant properly handled');
    }

    console.log('✅ Tenant validation tests passed\n');

  } catch (error: any) {
    console.error('❌ Tenant validation test failed:', error.message);
    throw error;
  }
}

// Run tests
async function runAllTests() {
  try {
    await testTenantValidation();
    await testMappingSystem();
  } catch (error) {
    process.exit(1);
  }
}

// Execute if run directly
if (require.main === module) {
  runAllTests();
}

export { testMappingSystem, testTenantValidation };
