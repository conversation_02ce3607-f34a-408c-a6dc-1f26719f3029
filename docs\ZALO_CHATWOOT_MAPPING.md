# Zalo-Chatwoot Mapping System

Hệ thống mapping giữa Zalo thread ID và Chatwoot contact/conversation để tránh duplicate và tối ưu hóa việc tìm kiếm.

## Tổng quan

Hệ thống này giải quyết các vấn đề:
- **Tránh duplicate**: Mapping chính xác giữa Zalo thread và Chatwoot conversation
- **Tối ưu tìm kiếm**: <PERSON><PERSON> thông tin để tránh gọi API Chatwoot nhiều lần
- **Multi-tenant**: Hỗ trợ nhiều tenant với RLS bảo mật
- **Tracking**: <PERSON> dõi số lượng tin nhắn và thời gian cuối

## Cấu trúc Database

### Bảng `zalo_chatwoot_mapping`

```sql
CREATE TABLE public.zalo_chatwoot_mapping (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES public.tenants(id),
    
    -- Zalo information
    zalo_thread_id VARCHAR(255) NOT NULL,
    zalo_user_id VARCHAR(255) NOT NULL,
    zalo_user_name VARCHAR(255),
    
    -- Chatwoot information
    chatwoot_contact_id INTEGER NOT NULL,
    chatwoot_conversation_id INTEGER,
    chatwoot_inbox_id INTEGER NOT NULL,
    chatwoot_source_id VARCHAR(255) NOT NULL,
    
    -- Status and metadata
    is_active BOOLEAN DEFAULT true,
    last_message_at TIMESTAMPTZ,
    message_count INTEGER DEFAULT 0,
    metadata JSONB DEFAULT '{}',
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now(),
    
    -- Constraints
    UNIQUE(tenant_id, zalo_thread_id),
    UNIQUE(tenant_id, chatwoot_source_id)
);
```

### Indexes

- `idx_zalo_chatwoot_mapping_tenant_id`: Tìm kiếm theo tenant
- `idx_zalo_chatwoot_mapping_zalo_thread_id`: Tìm kiếm theo Zalo thread ID
- `idx_zalo_chatwoot_mapping_zalo_user_id`: Tìm kiếm theo Zalo user ID
- `idx_zalo_chatwoot_mapping_chatwoot_contact_id`: Tìm kiếm theo Chatwoot contact ID
- `idx_zalo_chatwoot_mapping_active`: Lọc mapping active
- `idx_zalo_chatwoot_mapping_last_message`: Sắp xếp theo thời gian tin nhắn cuối

## Row Level Security (RLS)

### Policies

1. **SELECT**: Chỉ xem mapping của tenant hiện tại
2. **INSERT**: Chỉ tạo mapping cho tenant hiện tại
3. **UPDATE**: Chỉ cập nhật mapping của tenant hiện tại
4. **DELETE**: Chỉ xóa mapping của tenant hiện tại
5. **Service Role**: Bypass tất cả RLS policies

### Tenant Context

Service role sử dụng `app.current_tenant_id` setting để xác định tenant context:

```sql
SELECT set_config('app.current_tenant_id', 'tenant-uuid', true);
```

## API Endpoints

### Webhook Integration

```typescript
// POST /api/zalo-webhook
// Header: X-Tenant-ID: <tenant-uuid>
// Hoặc Query: ?tenant_id=<tenant-uuid>
```

### Mapping Management

```typescript
// GET /api/zalo-chatwoot-mapping/stats
// Lấy thống kê mapping

// GET /api/zalo-chatwoot-mapping/by-thread/:threadId
// Lấy mapping theo Zalo thread ID

// GET /api/zalo-chatwoot-mapping/by-user/:userId
// Lấy mapping theo Zalo user ID

// GET /api/zalo-chatwoot-mapping/by-contact/:contactId
// Lấy mapping theo Chatwoot contact ID

// PUT /api/zalo-chatwoot-mapping/:threadId
// Cập nhật mapping

// PUT /api/zalo-chatwoot-mapping/:threadId/deactivate
// Deactivate mapping
```

## Service Classes

### ZaloChatwootMappingService

```typescript
import { zaloChatwootMappingService } from '@/services/ZaloChatwootMappingService';

// Tìm mapping theo thread ID
const mapping = await zaloChatwootMappingService.findByZaloThreadId(tenantId, threadId);

// Tạo mapping mới
const newMapping = await zaloChatwootMappingService.createMapping({
  tenant_id: tenantId,
  zalo_thread_id: threadId,
  zalo_user_id: userId,
  chatwoot_contact_id: contactId,
  chatwoot_inbox_id: inboxId,
  chatwoot_source_id: sourceId
});

// Cập nhật message count
await zaloChatwootMappingService.incrementMessageCount(tenantId, threadId);
```

### ChatwootService (Updated)

```typescript
// Xử lý tin nhắn với mapping
const result = await chatwootService.processZaloMessage({
  from: userName,
  content: messageContent,
  userId: zaloUserId,
  messageId: messageId,
  timestamp: timestamp,
  threadId: threadId,
  tenantId: tenantId
});

// Kết quả bao gồm mapping
const { contact, conversation, message, mapping } = result;
```

## Flow Xử lý Tin nhắn

1. **Nhận webhook từ Zalo**
   - Validate tenant ID từ header/query
   - Extract thread ID, user ID, content

2. **Kiểm tra mapping hiện có**
   - Tìm mapping theo `tenant_id` + `zalo_thread_id`
   - Nếu có: sử dụng cached contact/conversation
   - Nếu không: tạo mới contact/conversation

3. **Xử lý với Chatwoot**
   - Lấy/tạo contact trong Chatwoot
   - Lấy/tạo conversation trong Chatwoot
   - Tạo message trong conversation

4. **Cập nhật mapping**
   - Tạo mapping mới (nếu chưa có)
   - Cập nhật message count và last_message_at
   - Cập nhật user name (nếu thay đổi)

## Cấu hình Environment

```env
# Supabase Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_KEY=your-service-role-key

# Chatwoot Configuration
CHATWOOT_BASE_URL=https://app.chatwoot.com
CHATWOOT_API_ACCESS_TOKEN=your-api-token
CHATWOOT_ACCOUNT_ID=your-account-id
CHATWOOT_INBOX_ID=your-inbox-id
```

## Monitoring và Logging

### Metrics quan trọng

- Số lượng mapping active/inactive
- Tổng số tin nhắn đã xử lý
- Thời gian xử lý trung bình
- Tỷ lệ cache hit/miss

### Log Events

- Mapping creation/update
- Message processing
- Cache hits/misses
- Error handling

## Best Practices

1. **Tenant Validation**: Luôn validate tenant ID trước khi xử lý
2. **Error Handling**: Graceful fallback khi mapping service fail
3. **Cache Strategy**: Sử dụng mapping để tránh gọi Chatwoot API
4. **Monitoring**: Track performance và error rates
5. **Cleanup**: Định kỳ cleanup mapping inactive

## Troubleshooting

### Common Issues

1. **Duplicate Conversations**: Kiểm tra unique constraints
2. **Missing Mappings**: Verify tenant context
3. **RLS Errors**: Ensure service role key configured
4. **Performance**: Check indexes và query optimization

### Debug Commands

```sql
-- Kiểm tra mapping của tenant
SELECT * FROM zalo_chatwoot_mapping WHERE tenant_id = 'tenant-uuid';

-- Thống kê mapping
SELECT 
  is_active,
  COUNT(*) as count,
  SUM(message_count) as total_messages
FROM zalo_chatwoot_mapping 
WHERE tenant_id = 'tenant-uuid'
GROUP BY is_active;

-- Mapping gần đây
SELECT * FROM zalo_chatwoot_mapping 
WHERE tenant_id = 'tenant-uuid'
ORDER BY last_message_at DESC 
LIMIT 10;
```
