{"name": "chatbot-zalo", "version": "1.0.0", "description": "Chatbot Zalo với TypeScript", "main": "dist/index.js", "type": "commonjs", "packageManager": "yarn@4.5.2", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "dev": "nodemon --exec ts-node src/index.ts", "start": "node dist/index.js", "clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "yarn clean", "type-check": "tsc --noEmit", "type-check:watch": "tsc --noEmit --watch", "format": "prettier --write src/**/*.ts", "format:check": "prettier --check src/**/*.ts", "check-all": "yarn type-check && yarn format:check", "qr-login": "ts-node src/scripts/qr-login.ts"}, "keywords": ["chatbot", "zalo", "typescript", "nodejs"], "author": "", "license": "MIT", "dependencies": {"@supabase/supabase-js": "^2.52.0", "axios": "^1.10.0", "body-parser": "^2.2.0", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^5.1.0", "helmet": "^8.1.0", "morgan": "^1.10.1", "zca-js": "^2.0.0-beta.25"}, "devDependencies": {"@types/axios": "^0.14.4", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/morgan": "^1.9.10", "@types/node": "^24.0.15", "nodemon": "^3.1.10", "prettier": "^3.6.2", "rimraf": "^6.0.1", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "engines": {"node": ">=18.0.0", "yarn": ">=4.0.0"}}