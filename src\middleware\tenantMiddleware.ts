import { Request, Response, NextFunction } from 'express';
import { AppError } from './errorHandler';
import { tenantService } from '@/services/TenantService';
import { logger } from '@/utils/logger';

// Extend Request interface để thêm tenant info
declare global {
  namespace Express {
    interface Request {
      tenantId?: string;
      tenant?: any;
    }
  }
}

/**
 * Middleware để validate và set tenant context
 */
export const validateTenant = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Lấy tenantId từ header, query parameter, hoặc body
    let tenantId = req.headers['x-tenant-id'] as string || 
                   req.query.tenant_id as string ||
                   req.body?.tenant_id as string;

    // Nếu không có tenantId, thử lấy tenant mặc định
    if (!tenantId) {
      logger.info('No tenant ID provided, trying to get default tenant');
      
      const defaultTenant = await tenantService.getDefaultTenant();
      if (defaultTenant) {
        tenantId = defaultTenant.id;
        logger.info('Using default tenant', {
          tenantId: defaultTenant.id,
          tenantName: defaultTenant.name
        });
      } else {
        throw new AppError('Tenant ID is required and no default tenant found', 400, 'MISSING_TENANT_ID');
      }
    }

    // Validate tenant exists và active
    const tenant = await tenantService.getTenantById(tenantId);
    if (!tenant) {
      throw new AppError('Invalid or inactive tenant', 400, 'INVALID_TENANT');
    }

    // Set tenant context
    await tenantService.setTenantContext(tenantId);

    // Attach tenant info to request
    req.tenantId = tenantId;
    req.tenant = tenant;

    logger.info('Tenant validated and context set', {
      tenantId: tenant.id,
      tenantName: tenant.name,
      requestId: req.id
    });

    next();
  } catch (error: any) {
    logger.error('Tenant validation failed', {
      error: error.message,
      requestId: req.id,
      headers: {
        'x-tenant-id': req.headers['x-tenant-id']
      },
      query: {
        tenant_id: req.query.tenant_id
      }
    });

    if (error instanceof AppError) {
      next(error);
    } else {
      next(new AppError('Tenant validation failed', 500, 'TENANT_VALIDATION_ERROR'));
    }
  }
};

/**
 * Middleware để validate tenant (optional - không throw error nếu không có)
 */
export const optionalTenant = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const tenantId = req.headers['x-tenant-id'] as string || 
                     req.query.tenant_id as string ||
                     req.body?.tenant_id as string;

    if (tenantId) {
      const tenant = await tenantService.getTenantById(tenantId);
      if (tenant) {
        await tenantService.setTenantContext(tenantId);
        req.tenantId = tenantId;
        req.tenant = tenant;
        
        logger.info('Optional tenant context set', {
          tenantId: tenant.id,
          tenantName: tenant.name,
          requestId: req.id
        });
      } else {
        logger.warn('Invalid tenant ID provided in optional context', {
          tenantId,
          requestId: req.id
        });
      }
    }

    next();
  } catch (error: any) {
    logger.warn('Optional tenant validation failed, continuing without tenant context', {
      error: error.message,
      requestId: req.id
    });
    next(); // Continue without tenant context
  }
};

/**
 * Helper function để lấy tenant ID từ request
 */
export const getTenantIdFromRequest = (req: Request): string | null => {
  return req.tenantId || 
         req.headers['x-tenant-id'] as string || 
         req.query.tenant_id as string ||
         req.body?.tenant_id as string ||
         null;
};

/**
 * Helper function để validate tenant ID format
 */
export const isValidTenantId = (tenantId: string): boolean => {
  // UUID format validation
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(tenantId);
};
